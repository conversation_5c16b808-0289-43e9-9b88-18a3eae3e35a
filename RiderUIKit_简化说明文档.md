# RiderUIKit SDK 简化说明文档

## 架构概览

### 设计理念

RiderUIKit 采用分层架构设计，核心理念如下：

- **SDK数据驱动**：所有数据来源于地图SDK，确保准确性和一致性
- **UI组件交互**：用户通过UI操作获取数据，无需直接调用SDK API
- **智能错误处理**：数据缺失时提供友好提示和解决方案
- **主题可配置**：支持颜色、图标的完全自定义和日夜模式
- **组件间协作**：通过数据管理器实现组件间自动数据共享

### API开放策略

- **每个自定义View只有一个统一的Callback接口**
- **只开放需要上层业务逻辑处理的点击事件**
- **内部UI更新的点击事件不开放，由组件内部自动处理**
- **主题配置完全开放，支持运行时动态更新**

### 架构图

```mermaid
graph TB
    subgraph "RiderUIKit SDK"
        subgraph "UI组件层"
            A[RiderMapView]
            B[RiderSearchView]
            C[RiderRouteView]
            D[RiderNavigationView]
        end

        subgraph "数据管理层"
            E[RiderUIDataManager<br/>- 组件间数据共享<br/>- 内存数据缓存<br/>- 智能错误处理]
        end

        subgraph "主题管理层"
            F[RiderUIThemeManager<br/>- 主题配置管理<br/>- 日夜模式切换<br/>- 全局主题应用]
        end

        subgraph "配置管理层"
            H[RiderUIConfigManager<br/>- 模拟导航配置<br/>- 调试模式配置<br/>- SDK功能配置]
        end

        subgraph "地图SDK集成层"
            G[RiderMap SDK<br/>- 地图显示<br/>- 搜索服务<br/>- 导航服务<br/>- 定位服务]
        end
    end

    A --> E
    B --> E
    C --> E
    D --> E

    A --> F
    B --> F
    C --> F
    D --> F

    A --> H
    B --> H
    C --> H
    D --> H

    E --> G
    F --> G
    H --> G

    %% 样式定义 - 适配暗色背景
    style A fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#ffffff
    style B fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#ffffff
    style C fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#ffffff
    style D fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#ffffff
    style E fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#ffffff
    style F fill:#4CAF50,stroke:#388E3C,stroke-width:2px,color:#ffffff
    style H fill:#E91E63,stroke:#C2185B,stroke-width:2px,color:#ffffff
    style G fill:#FF9800,stroke:#F57C00,stroke-width:2px,color:#ffffff

    %% 连接线样式
    linkStyle default stroke:#666666,stroke-width:2px
```

## SDK生命周期管理API

| 方法名称 | 参数 | 返回值 | 说明 |
|----------|------|--------|------|
| `RiderUIKit.initialize()` | `Context, RiderUIConfig` | `Unit` | 初始化SDK（配置对象方式） |
| `RiderUIKit.builder()` | `Context` | `RiderUIKitBuilder` | 创建Builder实例 |
| `RiderUIKit.destroy()` | - | `Unit` | 销毁SDK，清理资源 |
| `RiderUIKit.isInitialized()` | - | `Boolean` | 检查SDK是否已初始化 |

## SDK Builder API

| 方法名称 | 参数 | 返回值 | 说明 |
|----------|------|--------|------|
| `RiderUIKitBuilder.simulationMode()` | `Boolean` | `RiderUIKitBuilder` | 设置模拟导航模式 |
| `RiderUIKitBuilder.debugMode()` | `Boolean` | `RiderUIKitBuilder` | 设置调试模式 |
| `RiderUIKitBuilder.themeMode()` | `ThemeMode` | `RiderUIKitBuilder` | 设置主题模式 |
| `RiderUIKitBuilder.customTheme()` | `String` | `RiderUIKitBuilder` | 设置自定义主题 |
| `RiderUIKitBuilder.apiKey()` | `String` | `RiderUIKitBuilder` | 设置地图API密钥 |
| `RiderUIKitBuilder.build()` | - | `Unit` | 完成初始化 |

## SDK运行时配置API

| 方法名称 | 参数 | 返回值 | 说明 |
|----------|------|--------|------|
| `RiderUIKit.setSimulationMode()` | `Boolean` | `Unit` | 动态设置模拟导航模式 |
| `RiderUIKit.isSimulationModeEnabled()` | - | `Boolean` | 获取当前模拟导航状态 |
| `RiderUIKit.setDebugMode()` | `Boolean` | `Unit` | 动态设置调试模式 |
| `RiderUIKit.isDebugModeEnabled()` | - | `Boolean` | 获取当前调试模式状态 |
| `RiderUIKit.setNavigationCallback()` | `NavigationCallback` | `Unit` | 设置全局导航事件回调 |

## 全局导航回调API

| 回调接口名称 | 参数 | 返回值 | 说明 |
|-------------|------|--------|------|
| `NavigationCallback.onStartNavi()` | - | `Unit` | 导航开始事件 |
| `NavigationCallback.onStopNavi()` | - | `Unit` | 导航停止事件 |
| `NavigationCallback.onEndEmulatorNavi()` | - | `Unit` | 模拟导航结束事件 |
| `NavigationCallback.onArriveDestination()` | - | `Unit` | 到达目的地事件 |
| `NavigationCallback.onInitNaviSuccess()` | - | `Unit` | 导航初始化成功事件 |
| `NavigationCallback.onInitNaviFailure()` | - | `Unit` | 导航初始化失败事件 |
| `NavigationCallback.onNaviDataChanged()` | `NavigationInfo` | `Unit` | 导航数据更新事件 |
| `NavigationCallback.onGetNavigationText()` | `Int, String` | `Unit` | 导航语音播报事件 |
| `NavigationCallback.onGpsSignalWeak()` | `Boolean` | `Unit` | GPS信号弱事件 |

## 核心UI组件API

| 组件 | 方法/接口名称 | 参数 | 返回值 | 说明 |
|------|---------------|------|--------|------|
| **RiderMapView** | `setMapCallback()` | `MapCallback` | `Unit` | 设置地图交互回调 |
| | `updateTheme()` | `String` | `Unit` | 更新主题（使用主题名称） |
| | `MapCallback.onMapClick()` | `Double, Double` | `Unit` | 地图点击事件 |
| | `MapCallback.onMarkerClick()` | `String` | `Unit` | 标记点击事件 |
| **RiderSearchView** | `setSearchCallback()` | `SearchCallback` | `Unit` | 设置搜索回调 |
| | `updateTheme()` | `String` | `Unit` | 更新主题（使用主题名称） |
| | `SearchCallback.onSearchSubmitted()` | `String` | `Unit` | 搜索提交事件 |
| | `SearchCallback.onSearchResultItemClick()` | `SearchResultItem` | `Unit` | 搜索结果点击事件 |
| | `SearchCallback.onHistoryItemClick()` | `String` | `Unit` | 历史项点击事件 |
| **RiderRouteView** | `setRouteCallback()` | `RouteCallback` | `Unit` | 设置路线回调 |
| | `updateTheme()` | `String` | `Unit` | 更新主题（使用主题名称） |
| | `RouteCallback.onStartLocationButtonClick()` | - | `Unit` | 起点选择按钮点击 |
| | `RouteCallback.onDestinationButtonClick()` | - | `Unit` | 终点选择按钮点击 |
| | `RouteCallback.onCalculateRouteButtonClick()` | - | `Unit` | 计算路线按钮点击 |
| | `RouteCallback.onStartNavigationButtonClick()` | - | `Unit` | 开始导航按钮点击 |
| | `RouteCallback.onRouteOptionClick()` | `Int` | `Unit` | 路线选项点击 |
| **RiderNavigationView** | `setNavigationCallback()` | `NavigationCallback` | `Unit` | 设置导航回调 |
| | `updateTheme()` | `String` | `Unit` | 更新主题（使用主题名称） |
| | `NavigationCallback.onStopNavigationButtonClick()` | - | `Unit` | 停止导航按钮点击 |
| | `NavigationCallback.onNavigationSettingsButtonClick()` | - | `Unit` | 导航设置按钮点击 |
| | `NavigationCallback.onRouteOverviewButtonClick()` | - | `Unit` | 路线总览按钮点击 |
| | `NavigationCallback.onReportButtonClick()` | - | `Unit` | 上报按钮点击 |

## 主题管理API

| 方法名称 | 参数 | 返回值 | 说明 |
|----------|------|--------|------|
| `RiderUIKit.setGlobalTheme()` | `String` | `Unit` | 设置全局主题 |
| `RiderUIKit.setThemeMode()` | `ThemeMode` | `Unit` | 设置主题模式（日间/夜间/自动） |
| `RiderUIKit.getCurrentTheme()` | - | `String` | 获取当前主题名称 |

## 动态主题配置API

| 方法名称 | 参数 | 返回值 | 说明 |
|----------|------|--------|------|
| `MapRouteView.updateTheme()` | `RouteThemeConfig` | `Unit` | 更新路线组件主题配置 |
| `RiderAMapView.updateView()` | `RiderAMapViewConfig` | `Unit` | 更新地图组件配置 |

## SDK配置类API

| 配置类名称 | 属性 | 类型 | 默认值 | 说明 |
|------------|------|------|--------|------|
| `RiderUIConfig` | `apiKey` | `String` | - | 地图API密钥（必填） |
| | `simulationMode` | `Boolean` | `false` | 模拟导航模式 |
| | `debugMode` | `Boolean` | `false` | 调试模式 |
| | `themeMode` | `ThemeMode` | `AUTO` | 主题模式 |
| | `customTheme` | `String?` | `null` | 自定义主题名称 |

## 数据模型API

| 数据类名称 | 属性 | 类型 | 说明 |
|------------|------|------|------|
| `LocationInfo` | `latitude` | `Double` | 纬度 |
| | `longitude` | `Double` | 经度 |
| | `address` | `String` | 地址 |
| | `accuracy` | `Float` | 精度 |
| | `timestamp` | `Long` | 时间戳 |
| `SearchResultItem` | `id` | `String` | 结果ID |
| | `name` | `String` | 名称 |
| | `address` | `String` | 地址 |
| | `location` | `LocationInfo` | 位置信息 |
| | `category` | `String` | 分类 |
| | `distance` | `Float` | 距离 |
| | `rating` | `Float` | 评分 |
| `RouteInfo` | `routeId` | `String` | 路线ID |
| | `startLocation` | `LocationInfo` | 起点 |
| | `endLocation` | `LocationInfo` | 终点 |
| | `distance` | `Float` | 距离 |
| | `duration` | `Int` | 时长 |
| | `strategy` | `RouteStrategy` | 路线策略 |
| | `polylinePoints` | `List<LocationInfo>` | 路线点集合 |
| `NavigationInfo` | `currentRoadName` | `String` | 当前道路名称 |
| | `nextRoadName` | `String` | 下一条道路名称 |
| | `remainDistance` | `Int` | 剩余距离（米） |
| | `remainTime` | `Int` | 剩余时间（秒） |
| | `currentSpeed` | `Int` | 当前速度（km/h） |
| | `iconType` | `Int` | 转向图标类型 |
| | `turnDistance` | `Int` | 转向距离（米） |
| `RouteThemeConfig` | `naviButtonTextColor` | `Int?` | 导航按钮文字颜色 |
| | `naviButtonText` | `String?` | 导航按钮文字 |
| | `routeSelectedColor` | `Int?` | 选中路线颜色 |
| | `routeButtonSelectedTextColor` | `Int?` | 路线按钮选中文字颜色 |
| | `routeButtonUnselectedTextColor` | `Int?` | 路线按钮未选中文字颜色 |
| | `routeButtonSelectedBackgroundRes` | `Int?` | 路线按钮选中背景资源ID |
| | `routeButtonTrafficLightSelectedIconRes` | `Int?` | 路线按钮选中交通灯图标资源ID |
| | `trafficOnIconRes` | `Int?` | 交通开启图标资源ID |
| | `trafficOffIconRes` | `Int?` | 交通关闭图标资源ID |
| | `mapType` | `Int?` | 地图类型 |
| | `mapShadowColor` | `Int?` | 地图阴影颜色 |

## 预定义主题

| 主题名称 | 说明 |
|----------|------|
| `"day"` | 日间主题 |
| `"night"` | 夜间主题 |

## XML自定义属性API

### 主题属性

| 组件 | 属性名称 | 格式 | 说明 |
|------|----------|------|------|
| **所有组件** | `riderTheme` | `string` | 指定主题名称 |
| **RiderMapView** | `mapTheme` | `string` | 地图专用主题 |
| **RiderSearchView** | `searchTheme` | `string` | 搜索专用主题 |
| **RiderRouteView** | `routeTheme` | `string` | 路线专用主题 |
| **RiderNavigationView** | `navigationTheme` | `string` | 导航专用主题 |

### 颜色属性

| 组件 | 属性名称 | 格式 | 说明 |
|------|----------|------|------|
| **RiderMapView** | `mapShadowColor` | `color\|reference` | 地图阴影颜色 |
| | `mapSearchBackground` | `color\|reference` | 搜索框背景颜色 |
| | `mapSearchTextColor` | `color\|reference` | 搜索框文字颜色 |
| | `mapSearchHintColor` | `color\|reference` | 搜索框提示文字颜色 |
| **RiderSearchView** | `searchTextColor` | `color\|reference` | 搜索文字颜色 |
| | `searchHintColor` | `color\|reference` | 搜索提示文字颜色 |
| | `searchBackground` | `color\|reference` | 搜索背景颜色 |
| | `searchItemBackground` | `color\|reference` | 搜索项背景颜色 |
| **RiderRouteView** | `routeBackground` | `color\|reference` | 路线面板背景颜色 |
| | `routeNaviTextColor` | `color\|reference` | 导航按钮文字颜色 |
| | `routeTimeTextColor` | `color\|reference` | 时间文字颜色 |
| | `routeDistanceTextColor` | `color\|reference` | 距离文字颜色 |
| | `routeSelectedColor` | `color\|reference` | 选中路线颜色 |
| **RiderNavigationView** | `navigationBackground` | `color\|reference` | 导航背景颜色 |
| | `navigationDistanceColor` | `color\|reference` | 剩余距离文字颜色 |
| | `navigationTimeColor` | `color\|reference` | 剩余时间文字颜色 |
| | `navigationRightBarBackground` | `color\|reference` | 右侧控制栏背景颜色 |

### 图标属性

| 组件 | 属性名称 | 格式 | 说明 |
|------|----------|------|------|
| **RiderMapView** | `mapLocationIcon` | `reference` | 定位图标 |
| | `mapTrafficOnIcon` | `reference` | 路况开启图标 |
| | `mapTrafficOffIcon` | `reference` | 路况关闭图标 |
| | `mapSearchIcon` | `reference` | 搜索图标 |
| | `mapNavRouteIcon` | `reference` | 导航路线图标 |
| **RiderSearchView** | `searchIcon` | `reference` | 搜索图标 |
| | `searchClearIcon` | `reference` | 清空图标 |
| | `searchVoiceIcon` | `reference` | 语音图标 |
| | `searchHistoryIcon` | `reference` | 历史图标 |
| **RiderRouteView** | `routeStartIcon` | `reference` | 起点图标 |
| | `routeEndIcon` | `reference` | 终点图标 |
| | `routeSwapIcon` | `reference` | 交换图标 |
| | `routeCalculateIcon` | `reference` | 计算图标 |
| | `routeNaviIcon` | `reference` | 开始导航图标 |
| **RiderNavigationView** | `navigationExitIcon` | `reference` | 退出导航图标 |
| | `navigationTrafficOnIcon` | `reference` | 路况开启图标 |
| | `navigationTrafficOffIcon` | `reference` | 路况关闭图标 |
| | `navigationMapTypeDayIcon` | `reference` | 日间地图类型图标 |
| | `navigationMapTypeNightIcon` | `reference` | 夜间地图类型图标 |
| | `navigationOverviewIcon` | `reference` | 总览模式图标 |

## API使用示例

### 1. SDK初始化示例

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // 方式1：使用Builder模式（推荐）
        RiderUIKit.builder(this)
            .apiKey("your_amap_api_key")
            .simulationMode(BuildConfig.DEBUG)
            .debugMode(BuildConfig.DEBUG)
            .themeMode(ThemeMode.AUTO)
            .customTheme("day")
            .build()

        // 方式2：使用配置对象
        val config = RiderUIConfig(
            apiKey = "your_amap_api_key",
            simulationMode = true,
            debugMode = BuildConfig.DEBUG,
            themeMode = ThemeMode.AUTO,
            customTheme = "day"
        )
        RiderUIKit.initialize(this, config)

        // 方式3：最简初始化（只提供必要参数）
        RiderUIKit.builder(this)
            .apiKey("your_amap_api_key")
            .build()
    }

    override fun onTerminate() {
        super.onTerminate()
        RiderUIKit.destroy()
    }
}

// Builder模式的高级用法示例
class AdvancedInitialization {

    fun initializeForDevelopment(context: Context) {
        RiderUIKit.builder(context)
            .apiKey(getApiKeyFromBuildConfig())
            .simulationMode(true)  // 开发环境启用模拟导航
            .debugMode(true)       // 开启调试日志
            .themeMode(ThemeMode.AUTO)
            .build()
    }

    fun initializeForProduction(context: Context) {
        RiderUIKit.builder(context)
            .apiKey(getApiKeyFromSecureStorage())
            .simulationMode(false) // 生产环境关闭模拟导航
            .debugMode(false)      // 关闭调试日志
            .themeMode(ThemeMode.AUTO)
            .customTheme("day")
            .build()
    }

    fun initializeWithUserPreferences(context: Context, userPrefs: SharedPreferences) {
        val savedTheme = userPrefs.getString("theme", "day") ?: "day"
        val simulationEnabled = userPrefs.getBoolean("simulation_mode", false)

        RiderUIKit.builder(context)
            .apiKey(getApiKey())
            .simulationMode(simulationEnabled)
            .debugMode(BuildConfig.DEBUG)
            .customTheme(savedTheme)
            .build()
    }

    private fun getApiKeyFromBuildConfig(): String {
        return BuildConfig.AMAP_API_KEY
    }

    private fun getApiKeyFromSecureStorage(): String {
        // 从安全存储中获取API密钥
        return SecureStorage.getApiKey()
    }

    private fun getApiKey(): String {
        return if (BuildConfig.DEBUG) {
            getApiKeyFromBuildConfig()
        } else {
            getApiKeyFromSecureStorage()
        }
    }
}
```
```

### 2. 运行时配置示例

```kotlin
class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 动态切换模拟导航
        RiderUIKit.setSimulationMode(true)

        // 检查当前状态
        if (RiderUIKit.isSimulationModeEnabled()) {
            showToast("模拟导航已启用")
        }

        // 动态切换主题
        RiderUIKit.setGlobalTheme("night")
        RiderUIKit.setThemeMode(ThemeMode.NIGHT)

        // 设置全局导航回调
        RiderUIKit.setNavigationCallback(object : RiderUIKit.NavigationCallback {
            override fun onStartNavi() {
                Log.d("Navigation", "导航开始")
                // 可以在这里更新UI状态，比如隐藏搜索界面，显示导航界面
            }

            override fun onStopNavi() {
                Log.d("Navigation", "导航停止")
                // 可以在这里恢复UI状态
            }

            override fun onArriveDestination() {
                Log.d("Navigation", "到达目的地")
                showArrivalDialog()
            }

            override fun onEndEmulatorNavi() {
                Log.d("Navigation", "模拟导航结束")
                // 恢复UI状态
            }

            override fun onInitNaviSuccess() {
                Log.d("Navigation", "导航初始化成功")
            }

            override fun onInitNaviFailure() {
                Log.e("Navigation", "导航初始化失败")
                showErrorDialog("导航初始化失败")
            }

            override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
                Log.d("Navigation", "导航数据更新: ${navigationInfo.currentRoadName}")
                // 更新导航信息显示
            }

            override fun onGetNavigationText(type: Int, content: String) {
                Log.d("Navigation", "导航语音播报: $content")
                // 处理语音播报
            }

            override fun onGpsSignalWeak(isWeak: Boolean) {
                Log.d("Navigation", "GPS信号: ${if (isWeak) "弱" else "强"}")
                // 显示GPS信号状态
            }
        })
    }
}
```

### 3. UI组件使用示例

```kotlin
class MapActivity : AppCompatActivity() {
    private lateinit var mapView: RiderMapView
    private lateinit var searchView: RiderSearchView
    private lateinit var routeView: RiderRouteView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_map)

        initViews()
        setupCallbacks()
    }

    private fun initViews() {
        mapView = findViewById(R.id.map_view)
        searchView = findViewById(R.id.search_view)
        routeView = findViewById(R.id.route_view)

        // 设置主题
        mapView.updateTheme("day")
        searchView.updateTheme("day")
        routeView.updateTheme("day")
    }

    private fun setupCallbacks() {
        // 地图回调
        mapView.setMapCallback(object : RiderMapView.MapCallback {
            override fun onMapClick(latitude: Double, longitude: Double) {
                Log.d("Map", "点击位置: $latitude, $longitude")
            }

            override fun onMarkerClick(markerId: String) {
                Log.d("Map", "点击标记: $markerId")
            }
        })

        // 搜索回调
        searchView.setSearchCallback(object : RiderSearchView.SearchCallback {
            override fun onSearchSubmitted(query: String) {
                Log.d("Search", "搜索: $query")
            }

            override fun onSearchResultItemClick(item: SearchResultItem) {
                Log.d("Search", "选择结果: ${item.name}")
            }

            override fun onHistoryItemClick(query: String) {
                Log.d("Search", "历史搜索: $query")
            }
        })

        // 路线回调
        routeView.setRouteCallback(object : RiderRouteView.RouteCallback {
            override fun onStartLocationButtonClick() {
                // 打开起点选择界面
                startLocationPicker(true)
            }

            override fun onDestinationButtonClick() {
                // 打开终点选择界面
                startLocationPicker(false)
            }

            override fun onCalculateRouteButtonClick() {
                // 计算路线
                calculateRoute()
            }

            override fun onStartNavigationButtonClick() {
                // 开始导航
                startNavigation()
            }

            override fun onRouteOptionClick(routeIndex: Int) {
                Log.d("Route", "选择路线: $routeIndex")
            }
        })
    }
}
```

### 4. XML布局配置示例

```xml
<!-- activity_map.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 搜索组件 - 使用预定义主题 -->
    <com.link.rideramap.ui.RiderSearchView
        android:id="@+id/search_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:riderTheme="day" />

    <!-- 地图组件 - 自定义颜色 -->
    <com.link.rideramap.ui.RiderMapView
        android:id="@+id/map_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:mapShadowColor="@color/custom_shadow_color"
        app:mapSearchBackground="@color/custom_search_background"
        app:mapSearchTextColor="@color/custom_text_color"
        app:mapLocationIcon="@drawable/custom_location_icon"
        app:mapTrafficOnIcon="@drawable/custom_traffic_on_icon"
        app:mapTrafficOffIcon="@drawable/custom_traffic_off_icon" />

    <!-- 路线组件 - 混合配置 -->
    <com.link.rideramap.ui.RiderRouteView
        android:id="@+id/route_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:routeTheme="night"
        app:routeBackground="@color/custom_route_background"
        app:routeNaviTextColor="@color/custom_navi_text_color"
        app:routeSelectedColor="@color/custom_selected_color"
        app:routeStartIcon="@drawable/custom_start_icon"
        app:routeEndIcon="@drawable/custom_end_icon"
        app:routeNaviIcon="@drawable/custom_navi_icon" />

    <!-- 导航组件 - 自定义图标 -->
    <com.link.rideramap.ui.RiderNavigationView
        android:id="@+id/navigation_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:navigationTheme="night"
        app:navigationBackground="@color/custom_navi_background"
        app:navigationDistanceColor="@color/custom_distance_color"
        app:navigationRightBarBackground="@color/custom_right_bar_bg"
        app:navigationExitIcon="@drawable/custom_exit_icon"
        app:navigationTrafficOnIcon="@drawable/custom_traffic_on_icon"
        app:navigationOverviewIcon="@drawable/custom_overview_icon" />

</LinearLayout>
```

### 5. 数据模型使用示例

```kotlin
// 位置信息创建
val location = LocationInfo(
    latitude = 39.908823,
    longitude = 116.397470,
    address = "北京市东城区天安门广场",
    accuracy = 10.0f,
    timestamp = System.currentTimeMillis()
)

// 搜索结果处理
fun handleSearchResult(item: SearchResultItem) {
    Log.d("Search", """
        搜索结果:
        名称: ${item.name}
        地址: ${item.address}
        位置: ${item.location.latitude}, ${item.location.longitude}
        分类: ${item.category}
        距离: ${item.distance}m
        评分: ${item.rating}
    """.trimIndent())
}

// 路线信息处理
fun handleRouteInfo(route: RouteInfo) {
    Log.d("Route", """
        路线信息:
        ID: ${route.routeId}
        起点: ${route.startLocation.address}
        终点: ${route.endLocation.address}
        距离: ${route.distance}km
        时长: ${route.duration}分钟
        策略: ${route.strategy}
        路线点数: ${route.polylinePoints.size}
    """.trimIndent())
}
```

### 6. 主题切换示例

```kotlin
class ThemeManager {

    // 根据时间自动切换主题
    fun autoSwitchTheme() {
        val hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        val theme = if (hour in 6..18) "day" else "night"
        RiderUIKit.setGlobalTheme(theme)
    }

    // 用户手动切换主题
    fun switchToTheme(themeName: String) {
        when (themeName) {
            "day" -> RiderUIKit.setThemeMode(ThemeMode.DAY)
            "night" -> RiderUIKit.setThemeMode(ThemeMode.NIGHT)
            "auto" -> RiderUIKit.setThemeMode(ThemeMode.AUTO)
            else -> RiderUIKit.setGlobalTheme(themeName)
        }
    }

    // 获取当前主题状态
    fun getCurrentThemeInfo(): String {
        val currentTheme = RiderUIKit.getCurrentTheme()
        val isSimulation = RiderUIKit.isSimulationModeEnabled()
        val isDebug = RiderUIKit.isDebugModeEnabled()

        return "主题: $currentTheme, 模拟导航: $isSimulation, 调试模式: $isDebug"
    }
}
```

### 7. 完整应用示例

```kotlin
class NavigationActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查SDK初始化状态
        if (!RiderUIKit.isInitialized()) {
            Log.e("Navigation", "SDK未初始化")
            finish()
            return
        }

        setContentView(R.layout.activity_navigation)

        // 根据用户偏好配置
        setupUserPreferences()

        // 初始化UI组件
        setupUIComponents()

        // 设置全局导航回调
        setupNavigationCallback()
    }

    private fun setupUserPreferences() {
        val prefs = getSharedPreferences("user_prefs", Context.MODE_PRIVATE)

        // 恢复用户的主题偏好
        val savedTheme = prefs.getString("theme", "day")
        RiderUIKit.setGlobalTheme(savedTheme!!)

        // 恢复模拟导航设置
        val simulationMode = prefs.getBoolean("simulation_mode", false)
        RiderUIKit.setSimulationMode(simulationMode)
    }

    private fun setupUIComponents() {
        // 设置统一主题
        val currentTheme = RiderUIKit.getCurrentTheme()

        findViewById<RiderMapView>(R.id.map_view).updateTheme(currentTheme)
        findViewById<RiderSearchView>(R.id.search_view).updateTheme(currentTheme)
        findViewById<RiderRouteView>(R.id.route_view).updateTheme(currentTheme)
    }

    private fun setupNavigationCallback() {
        RiderUIKit.setNavigationCallback(object : RiderUIKit.NavigationCallback {
            override fun onStartNavi() {
                // 导航开始时的UI处理
                supportActionBar?.title = "导航中"
                // 可以隐藏搜索界面，显示导航控制界面
                findViewById<RiderSearchView>(R.id.search_view).visibility = View.GONE
                showNavigationControls()
            }

            override fun onStopNavi() {
                // 导航停止时的UI处理
                supportActionBar?.title = "地图"
                findViewById<RiderSearchView>(R.id.search_view).visibility = View.VISIBLE
                hideNavigationControls()
            }

            override fun onArriveDestination() {
                // 到达目的地
                showArrivalNotification("目的地")
                onStopNavi() // 恢复UI状态
            }

            override fun onEndEmulatorNavi() {
                // 模拟导航结束
                showToast("模拟导航结束")
                onStopNavi() // 恢复UI状态
            }

            override fun onInitNaviFailure() {
                // 导航初始化失败
                Toast.makeText(this@NavigationActivity, "导航初始化失败", Toast.LENGTH_LONG).show()
            }

            override fun onInitNaviSuccess() {
                // 导航初始化成功
                Log.d("Navigation", "导航初始化成功")
            }

            override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
                // 导航数据更新，可以更新导航信息显示
                updateNavigationDisplay(navigationInfo)
            }

            override fun onGetNavigationText(type: Int, content: String) {
                // 导航语音播报
                Log.d("Navigation", "语音播报: $content")
            }

            override fun onGpsSignalWeak(isWeak: Boolean) {
                // GPS信号状态
                if (isWeak) {
                    showToast("GPS信号弱")
                }
            }
        })
    }

    private fun showNavigationControls() {
        // 显示导航控制界面的逻辑
    }

    private fun hideNavigationControls() {
        // 隐藏导航控制界面的逻辑
    }

    private fun showArrivalNotification(address: String) {
        AlertDialog.Builder(this)
            .setTitle("到达目的地")
            .setMessage("您已到达: $address")
            .setPositiveButton("确定", null)
            .show()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun updateNavigationDisplay(navigationInfo: NavigationInfo) {
        // 更新导航信息显示
        Log.d("Navigation", """
            导航信息更新:
            当前道路: ${navigationInfo.currentRoadName}
            下一条道路: ${navigationInfo.nextRoadName}
            剩余距离: ${navigationInfo.remainDistance}米
            剩余时间: ${navigationInfo.remainTime}秒
            当前速度: ${navigationInfo.currentSpeed}km/h
            转向距离: ${navigationInfo.turnDistance}米
        """.trimIndent())

        // 这里可以更新UI显示导航信息
        // 例如更新导航面板的文字和图标
    }

    override fun onDestroy() {
        super.onDestroy()

        // 保存用户偏好
        val prefs = getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString("theme", RiderUIKit.getCurrentTheme())
            .putBoolean("simulation_mode", RiderUIKit.isSimulationModeEnabled())
            .apply()
    }
}
```

## 动态主题配置示例

### MapRouteView 主题配置

```kotlin
// 创建日间主题配置
val dayTheme = RouteThemeConfig(
    naviButtonTextColor = Color.parseColor("#5C7BD7"),
    naviButtonText = "开始导航",
    routeSelectedColor = Color.parseColor("#5C7BD7"),
    routeTimeTextColor = Color.parseColor("#191919"),
    mapType = AMap.MAP_TYPE_NORMAL
)

// 创建夜间主题配置
val nightTheme = RouteThemeConfig(
    naviButtonTextColor = Color.WHITE,
    naviButtonText = "开始导航",
    routeSelectedColor = Color.WHITE,
    routeTimeTextColor = Color.WHITE,
    mapType = AMap.MAP_TYPE_NIGHT
)

// 应用主题
mapRouteView.updateTheme(if (isNightMode) nightTheme else dayTheme)

// 动态切换主题
fun toggleTheme() {
    isNightMode = !isNightMode
    val newTheme = if (isNightMode) nightTheme else dayTheme
    mapRouteView.updateTheme(newTheme)
}
```

### 自定义品牌主题

```kotlin
// 创建品牌主题
val brandTheme = RouteThemeConfig(
    naviButtonTextColor = Color.parseColor("#FF6B35"), // 品牌橙色
    naviButtonText = "开始骑行",
    routeSelectedColor = Color.parseColor("#FF6B35"),
    routeButtonSelectedTextColor = Color.parseColor("#FF6B35"),
    routeButtonUnselectedTextColor = Color.parseColor("#666666"),
    mapType = AMap.MAP_TYPE_NORMAL
)

mapRouteView.updateTheme(brandTheme)
```

### RouteButtonView 主题配置

```kotlin
// RouteButtonView 专用配置
val routeButtonTheme = RouteThemeConfig(
    routeButtonSelectedTextColor = Color.parseColor("#FF6B35"),
    routeButtonUnselectedTextColor = Color.parseColor("#666666"),
    routeButtonSelectedBackgroundRes = R.drawable.custom_selected_bg,
    routeButtonTrafficLightSelectedIconRes = R.drawable.custom_traffic_icon
)

mapRouteView.updateTheme(routeButtonTheme)
```

### 配置合并

```kotlin
// 基础配置
val baseConfig = createDayTheme()

// 覆盖配置
val overrideConfig = RouteThemeConfig(
    naviButtonText = "立即出发",
    routeButtonSelectedTextColor = Color.parseColor("#FF6B35")
)

// 合并配置（override 优先于 base）
val finalConfig = RouteThemeConfig.merge(overrideConfig, baseConfig)
mapRouteView.updateTheme(finalConfig)
```

### XML 配置方式

```xml
<com.link.rideramap.presentation.component.route.MapRouteView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:route_naviButtonTextColor="@color/brand_color"
    app:route_naviButtonText="开始骑行"
    app:route_trafficOnIcon="@drawable/custom_traffic_icon"
    app:route_mapShadowColor="@color/custom_shadow" />
```
