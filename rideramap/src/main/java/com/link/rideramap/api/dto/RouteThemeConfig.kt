package com.link.rideramap.api.dto

import android.graphics.drawable.Drawable
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes

/**
 * 路线组件主题配置数据类
 * 
 * 提供路线组件的完整主题配置，包括：
 * - 背景和按钮样式配置
 * - 文字颜色配置
 * - 图标资源配置
 * - 导航相关UI配置
 */
data class RouteThemeConfig(
    // 背景配置
    @DrawableRes val routeBackgroundRes: Int? = null,
    val routeBackgroundDrawable: Drawable? = null,
    
    @DrawableRes val routeSelectBackgroundRes: Int? = null,
    val routeSelectBackgroundDrawable: Drawable? = null,
    
    @DrawableRes val startEndBackgroundRes: Int? = null,
    val startEndBackgroundDrawable: Drawable? = null,
    
    // 导航按钮配置
    @DrawableRes val naviButtonBackgroundRes: Int? = null,
    val naviButtonBackgroundDrawable: Drawable? = null,
    
    @ColorInt val naviButtonTextColor: Int? = null,
    val naviButtonText: String? = null,
    
    // 返回按钮配置
    @DrawableRes val backButtonIconRes: Int? = null,
    val backButtonIconDrawable: Drawable? = null,
    
    // RouteButtonView 配置
    @DrawableRes val routeButtonSelectedBackgroundRes: Int? = null,
    val routeButtonSelectedBackgroundDrawable: Drawable? = null,

    @DrawableRes val routeButtonUnselectedBackgroundRes: Int? = null,
    val routeButtonUnselectedBackgroundDrawable: Drawable? = null,

    @ColorInt val routeButtonSelectedTextColor: Int? = null,
    @ColorInt val routeButtonUnselectedTextColor: Int? = null,

    @DrawableRes val routeButtonTrafficLightSelectedIconRes: Int? = null,
    val routeButtonTrafficLightSelectedIconDrawable: Drawable? = null,

    @DrawableRes val routeButtonTrafficLightUnselectedIconRes: Int? = null,
    val routeButtonTrafficLightUnselectedIconDrawable: Drawable? = null,

    // 文字颜色配置（保持向后兼容）
    @ColorInt val routeTimeTextColor: Int? = null,
    @ColorInt val routeDistanceTextColor: Int? = null,
    @ColorInt val routeSelectedColor: Int? = null,
    
    // 导航相关图标配置
    @DrawableRes val trafficOnIconRes: Int? = null,
    val trafficOnIconDrawable: Drawable? = null,
    
    @DrawableRes val trafficOffIconRes: Int? = null,
    val trafficOffIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeAutoIconRes: Int? = null,
    val mapTypeAutoIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeDayIconRes: Int? = null,
    val mapTypeDayIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeNightIconRes: Int? = null,
    val mapTypeNightIconDrawable: Drawable? = null,
    
    @DrawableRes val overviewModeIconRes: Int? = null,
    val overviewModeIconDrawable: Drawable? = null,
    
    @DrawableRes val lockModeIconRes: Int? = null,
    val lockModeIconDrawable: Drawable? = null,
    
    @DrawableRes val exitNaviIconRes: Int? = null,
    val exitNaviIconDrawable: Drawable? = null,
    
    // 导航UI背景配置
    @DrawableRes val naviRightBarBackgroundRes: Int? = null,
    val naviRightBarBackgroundDrawable: Drawable? = null,
    
    @DrawableRes val naviBottomBarBackgroundRes: Int? = null,
    val naviBottomBarBackgroundDrawable: Drawable? = null,
    
    // 导航文字颜色配置
    @ColorInt val naviTimeTextColor: Int? = null,
    @ColorInt val naviDistanceTextColor: Int? = null,
    
    // 地图阴影配置
    @ColorInt val mapShadowColor: Int? = null,
    
    // 地图类型配置
    val mapType: Int? = null
)
