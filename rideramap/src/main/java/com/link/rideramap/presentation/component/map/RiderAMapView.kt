package com.link.rideramap.presentation.component.map

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.CustomRenderer
import com.amap.api.maps.TextureMapView
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.SPRiderAMap
import com.link.rideramap.api.dto.RiderAMapViewConfig
import com.link.rideramap.databinding.RiderAmapViewBinding
import com.link.rideramap.presentation.utils.AutoSizeUtils
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

/**
 * 简单的地图视图组件
 *
 * 提供以下核心功能：
 * - 地图显示和基础交互
 * - 定位标记的显示和管理
 * - 搜索标记的添加和移除
 * - 交通状况的开关控制
 * - 用户交互事件的监听
 */
class RiderAMapView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding = RiderAmapViewBinding.inflate(LayoutInflater.from(context), this, true)
    private lateinit var aMap: AMap

    private var mapView: TextureMapView = binding.map
    private var locationMarker: Marker? = null
    private var searchOrClickMarker: Marker? = null
    private var callback: RiderMaViewCallback? = null
    private val defaultLocateIcon = ContextCompat.getDrawable(context, R.drawable.mylocation)
    private val defaultTrafficOnIcon =
        ContextCompat.getDrawable(context, R.drawable.btn_road_condition_on)
    private val defaultTrafficOffIcon =
        ContextCompat.getDrawable(context, R.drawable.btn_road_condition_off)
    private val defaultSearchIcon = ContextCompat.getDrawable(context, R.drawable.btn_map_search)
    private val defaultNavIcon = ContextCompat.getDrawable(context, R.drawable.btn_map_navroute)
    private val defaultSearchBarBg =
        ContextCompat.getDrawable(context, R.drawable.search_background)
    private val defaultSearchHintText = context.getString(R.string.ramap_search_hint)
    private val defaultSearchHintTextColor =
        ContextCompat.getColor(context, R.color.ramap_search_hint_text_color)
    private val defaultSearchTextColor =
        ContextCompat.getColor(context, R.color.ramap_search_text_color)
    private val defaultMyLocationMarkerResId = R.drawable.nearme
    private val defaultSearchMarkerResId = R.drawable.location
    private val defaultShadowColor = ContextCompat.getColor(context, R.color.ramap_map_shadow_color)
    private var xmlConfig = RiderAMapViewConfig()
    private var programmaticConfig = RiderAMapViewConfig()
    private var hasActiveSearchMarker = false

    init {
        AutoSizeUtils.updateResources(context)
        loadAttributesFromXml(attrs, defStyleAttr)
        initializeMap()
        setupEventListeners()
        render()
    }

    private fun loadAttributesFromXml(attrs: AttributeSet?, defStyleAttr: Int) {
        attrs ?: return
        val typedArray = context.theme.obtainStyledAttributes(
            attrs, R.styleable.RiderAMapView, defStyleAttr, 0
        )
        try {
            xmlConfig = RiderAMapViewConfig(
                locateButtonIconDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_locateButtonIcon),
                trafficOnButtonIconDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_trafficOnButtonIcon),
                trafficOffButtonIconDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_trafficOffButtonIcon),
                searchButtonIconDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_searchButtonIcon),
                navigateButtonIconDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_navigateButtonIcon),
                searchBarBackgroundDrawable = typedArray.getDrawable(R.styleable.RiderAMapView_ramap_searchBarBackground),
                searchBarHintText = typedArray.getString(R.styleable.RiderAMapView_ramap_searchBarHintText),
                searchBarHintTextColor = typedArray.getColor(
                    R.styleable.RiderAMapView_ramap_searchBarHintTextColor,
                    0
                ),
                searchBarTextColor = typedArray.getColor(
                    R.styleable.RiderAMapView_ramap_searchBarTextColor,
                    0
                ),
                myLocationMarkerIconRes = typedArray.getResourceId(
                    R.styleable.RiderAMapView_ramap_myLocationMarkerIcon,
                    0
                ),
                searchResultMarkerIconRes = typedArray.getResourceId(
                    R.styleable.RiderAMapView_ramap_searchResultMarkerIcon,
                    0
                ),
                mapShadowColor = typedArray.getColor(
                    R.styleable.RiderAMapView_ramap_mapShadowColor,
                    0
                )
            )
        } finally {
            typedArray.recycle()
        }
    }

    private fun render() {
        // --- 定位按钮 ---
        val finalLocateIcon = programmaticConfig.locateButtonIconDrawable
            ?: programmaticConfig.locateButtonIconRes?.let {
                ContextCompat.getDrawable(
                    context,
                    it
                )
            }
            ?: xmlConfig.locateButtonIconDrawable
            ?: defaultLocateIcon
        binding.ivLocate.setImageDrawable(finalLocateIcon)

        // --- 交通按钮 (根据 aMap.isTrafficEnabled 状态) ---
        val finalTrafficIcon = if (aMap.isTrafficEnabled) {
            programmaticConfig.trafficOnButtonIconDrawable
                ?: programmaticConfig.trafficOnButtonIconRes?.let {
                    ContextCompat.getDrawable(
                        context,
                        it
                    )
                }
                ?: xmlConfig.trafficOnButtonIconDrawable
                ?: defaultTrafficOnIcon
        } else {
            programmaticConfig.trafficOffButtonIconDrawable
                ?: programmaticConfig.trafficOffButtonIconRes?.let {
                    ContextCompat.getDrawable(
                        context,
                        it
                    )
                }
                ?: xmlConfig.trafficOffButtonIconDrawable
                ?: defaultTrafficOffIcon
        }
        binding.ivTraffic.setImageDrawable(finalTrafficIcon)

        // --- 搜索/导航按钮 (根据 hasActiveSearchMarker 状态) ---
        val finalSearchOrNavIcon = if (hasActiveSearchMarker) {
            programmaticConfig.navigateButtonIconDrawable
                ?: programmaticConfig.navigateButtonIconRes?.let {
                    ContextCompat.getDrawable(
                        context,
                        it
                    )
                }
                ?: xmlConfig.navigateButtonIconDrawable
                ?: defaultNavIcon
        } else {
            programmaticConfig.searchButtonIconDrawable
                ?: programmaticConfig.searchButtonIconRes?.let {
                    ContextCompat.getDrawable(
                        context,
                        it
                    )
                }
                ?: xmlConfig.searchButtonIconDrawable
                ?: defaultSearchIcon
        }
        binding.ibSearch.background = finalSearchOrNavIcon

        // --- 搜索框 ---
        binding.linearLayoutCompat.background = programmaticConfig.searchBarBackgroundDrawable
            ?: programmaticConfig.searchBarBackgroundRes?.let {
                ContextCompat.getDrawable(
                    context,
                    it
                )
            }
                    ?: xmlConfig.searchBarBackgroundDrawable
                    ?: defaultSearchBarBg

        val hintText = programmaticConfig.searchBarHintText ?: xmlConfig.searchBarHintText
        ?: defaultSearchHintText
        binding.tvAddressName.hint = hintText

        val hintTextColor = programmaticConfig.searchBarHintTextColor
            ?: xmlConfig.searchBarHintTextColor.takeIf { it != 0 } ?: defaultSearchHintTextColor
        binding.tvAddressName.setHintTextColor(hintTextColor)

        val textColor =
            programmaticConfig.searchBarTextColor ?: xmlConfig.searchBarTextColor.takeIf { it != 0 }
            ?: defaultSearchTextColor
        binding.tvAddressName.setTextColor(textColor)

        // --- 地图阴影 ---
        val shadowColor =
            programmaticConfig.mapShadowColor ?: xmlConfig.mapShadowColor.takeIf { it != 0 }
            ?: defaultShadowColor
        binding.vMapShadow.setBackgroundColor(shadowColor)
    }

    private fun initializeMap() {
        aMap = mapView.map.apply {
            // 启用交通状况显示
            isTrafficEnabled = true
            // 设置自定义渲染器
            setCustomRenderer(createCustomRenderer())
            // 设置默认地图模式
            mapType = SPRiderAMap.Companion.instance.getDefaultMode()
        }

        // 在 aMap 赋值完成后配置地图设置
        configureMapUI()
        configureMapSettings()
    }

    private fun configureMapUI() {
        aMap.uiSettings.apply {
            isZoomControlsEnabled = false
            isIndoorSwitchEnabled = false
        }
    }

    private fun configureMapSettings() {
        aMap.apply {
            showBuildings(false)
            setRenderFps(MAP_RENDER_FPS)
            moveCamera(CameraUpdateFactory.zoomTo(DEFAULT_ZOOM_LEVEL))
        }
    }


    private fun setupEventListeners() {
        // 交通状况按钮点击事件
        binding.ivTraffic.setOnClickListener {
            aMap.isTrafficEnabled = !aMap.isTrafficEnabled
            render()
        }

        // 地图点击事件 - 清除搜索标记和地址名称
        aMap.setOnMapClickListener { latLng ->
            latLng?.let {
                removeMarker()
                setSearchName("")
            }
        }

        aMap.setOnMapLongClickListener { it ->
            MainScope().launch {
                SPRiderAMap.instance.geocodeSearch(context, it.latitude, it.longitude)
                    ?.let { locationInfo ->
                        //todo 保存目的地
                        if (locationInfo.code == 1000) {
                            val markerIconRes = programmaticConfig.searchResultMarkerIconRes
                                ?: xmlConfig.searchResultMarkerIconRes.takeIf { it != 0 }
                                ?: defaultSearchMarkerResId
                            val bitmap =
                                BitmapFactory.decodeResource(context.resources, markerIconRes)
                            addMarker(locationInfo.latitude, locationInfo.longitude, bitmap)
                            setSearchName(locationInfo.address)
                        }
                    }
            }
        }

        binding.ivLocate.setOnClickListener {
            MainScope().launch {
                val locationInfo = SPRiderAMap.instance.startLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
            }
        }

        binding.ibSearch.setOnClickListener {
            callback?.onSearchButtonClick()
        }
    }

    private fun createCustomRenderer(): CustomRenderer {
        return object : CustomRenderer {
            override fun onSurfaceCreated(openGl: GL10?, config: EGLConfig?) {
                // 移除自定义渲染器，避免重复调用
                aMap.setCustomRenderer(null)
                // 在主线程中隐藏阴影视图
                mapView.post {
                    binding.vMapShadow.visibility = GONE
                }
            }

            override fun onSurfaceChanged(openGl: GL10?, width: Int, height: Int) {
                // 地图表面尺寸变化时的处理
            }

            override fun onDrawFrame(openGl: GL10?) {
                // 地图绘制帧时的处理
            }

            override fun OnMapReferencechanged() {
                // 地图引用变化时的处理
            }
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        mapView.onCreate(null)
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == VISIBLE) {
            mapView.onResume()
        } else {
            mapView.onPause()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mapView.onResume()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 清理资源
        cleanupMarkers()
        mapView.onDestroy()
    }


    private fun cleanupMarkers() {
        locationMarker?.remove()
        searchOrClickMarker?.remove()
    }


    private fun addMarker(latitude: Double, longitude: Double, bitmap: Bitmap) {
        // 移除已存在的搜索标记
        searchOrClickMarker?.remove()

        val latLng = LatLng(latitude, longitude)
        searchOrClickMarker = aMap.addMarker(
            MarkerOptions()
                .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .anchor(SEARCH_MARKER_ANCHOR_X, SEARCH_MARKER_ANCHOR_Y)
                .position(latLng)
                .zIndex(SEARCH_MARKER_Z_INDEX)
                .visible(true)
        )
        hasActiveSearchMarker = true
        render()
    }

    private fun removeMarker() {
        searchOrClickMarker?.remove()
        hasActiveSearchMarker = false
        render()
    }

    private fun setPosition(latitude: Double, longitude: Double) {
        val latLng = LatLng(latitude, longitude)
        // 移动地图中心到指定位置
        aMap.moveCamera(CameraUpdateFactory.newLatLng(latLng))
        // 更新定位标记
        updateLocationMarker(latLng)
    }


    private fun updateLocationMarker(latLng: LatLng) {
        // 移除旧的定位标记
        locationMarker?.remove()
        val finalMyLocationMarkerRes = programmaticConfig.myLocationMarkerIconRes
            ?: xmlConfig.myLocationMarkerIconRes.takeIf { it != 0 } ?: defaultMyLocationMarkerResId
        val markerOptions = MarkerOptions().apply {
            anchor(LOCATION_MARKER_ANCHOR_X, LOCATION_MARKER_ANCHOR_Y)
            isFlat = true
            visible(true)
            zIndex(LOCATION_MARKER_Z_INDEX)
            icon(BitmapDescriptorFactory.fromResource(finalMyLocationMarkerRes))
            position(latLng)
        }

        locationMarker = aMap.addMarker(markerOptions)
    }

    private fun setSearchName(addressName: String) {
        binding.tvAddressName.text = addressName
    }

    fun getSearchName(): CharSequence {
        return binding.tvAddressName.text
    }

    /**
     * 设置点击事件监听器
     * @param riderMapViewCallback 点击事件回调
     */
    fun setCallback(riderMapViewCallback: RiderMaViewCallback) {
        callback = riderMapViewCallback
    }

    fun updateView(config: RiderAMapViewConfig) {
        this.programmaticConfig = config
        // 如果配置中包含地图类型，则更新
        config.mapType?.let { aMap.mapType = it }
        // 重新渲染以应用更改
        render()
    }

    interface RiderMaViewCallback {
        fun onSearchButtonClick()
    }


    companion object {
        private val TAG = RiderAMapView::class.java.simpleName

        // 地图阴影配置
        private const val MAP_SHADOW_ALPHA = 0xFF
        private const val MAP_SHADOW_COLOR_R = 0XEB
        private const val MAP_SHADOW_COLOR_G = 0XEB
        private const val MAP_SHADOW_COLOR_B = 0XEB

        // 地图基础配置
        private const val MAP_RENDER_FPS = 15
        private const val DEFAULT_ZOOM_LEVEL = 15f

        // 标记配置 - 搜索标记
        private const val SEARCH_MARKER_ANCHOR_X = 0.5f
        private const val SEARCH_MARKER_ANCHOR_Y = 0.85f
        private const val SEARCH_MARKER_Z_INDEX = 1.6f

        // 标记配置 - 定位标记
        private const val LOCATION_MARKER_ANCHOR_X = 0.5f
        private const val LOCATION_MARKER_ANCHOR_Y = 0.5f
        private const val LOCATION_MARKER_Z_INDEX = 2.2f
    }
}

