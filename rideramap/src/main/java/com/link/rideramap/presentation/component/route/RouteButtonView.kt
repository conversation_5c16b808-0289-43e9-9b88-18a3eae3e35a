package com.link.rideramap.presentation.component.route

import android.content.Context
import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import com.link.rideramap.R
import com.link.rideramap.databinding.RouteViewBinding
import com.link.rideramap.common.ext.formatDistance
import com.link.rideramap.common.ext.formatTime
import com.link.rideramap.core.search.domain.entity.RouteData
import java.text.SimpleDateFormat
import java.util.*
import androidx.core.graphics.toColorInt

internal class RouteButtonView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : ConstraintLayout(context, attrs, defStyle) {
    private val binding: RouteViewBinding =
        RouteViewBinding.inflate(LayoutInflater.from(context), this, true)

    private var isNight = false
    private fun format(total: Int, textView: TextView) {
        val minutes = String.format("%s 分钟", total)
        SpannableStringBuilder(minutes).also {
            it.setSpan(
                48,
                0,
                minutes.length - 4,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            it.setSpan(
                48,
                minutes.length - 4,
                minutes.length,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            textView.text = it
        }
    }

    fun setData(title: String, routeData: RouteData, isNight: Boolean) {
        binding.titleTv.text = title
        binding.totalMinutesTv.apply {
            format(routeData.formatTime(), this)
        }
        binding.trafficLightNumTv.apply {
            text = routeData.trafficLights.toString()
        }
        binding.distanceTv.apply {
            text = routeData.formatDistance()
        }
        binding.timeTv.apply {
            text = formatRouteTime(routeData.allTime)
        }
        this.isNight = isNight
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        format(0, binding.totalMinutesTv)
    }


    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        initTheme(selected)
    }

    fun initTheme(selected: Boolean){
        if (selected) {
            background = ResourcesCompat.getDrawable(resources,selectThemeInt(R.drawable.gpsmode_bg_s, R.drawable.gpsmode_bg_s_night), null)
            binding.trafficLightNumImg.setImageResource(R.drawable.traffic_light_num)
            binding.titleTv.setTextColor("#5C7BD7".toColorInt())
            binding.timeTv.setTextColor("#5C7BD7".toColorInt())
            binding.totalMinutesTv.setTextColor("#5C7BD7".toColorInt())
            binding.trafficLightNumTv.setTextColor("#5C7BD7".toColorInt())
            binding.distanceTv.setTextColor("#5C7BD7".toColorInt())
            binding.timeTv.setTextColor("#5C7BD7".toColorInt())
        } else {
            setBackgroundColor("#00000000".toColorInt())
            val color = selectThemeString("#202229","#FFFFFF")
            binding.trafficLightNumImg.setImageResource(selectThemeInt(R.drawable.traffic_light_num_idle, R.drawable.traffic_light_num_idle_night))
            binding.titleTv.setTextColor(color.toColorInt())
            binding.timeTv.setTextColor(color.toColorInt())
            binding.totalMinutesTv.setTextColor(color.toColorInt())
            binding.trafficLightNumTv.setTextColor(color.toColorInt())
            binding.distanceTv.setTextColor(color.toColorInt())
            binding.timeTv.setTextColor(color.toColorInt())
        }
    }

    fun selectThemeInt(day: Int, night: Int): Int{
        return if(isNight){
            night
        }else{
            day
        }
    }
    fun selectThemeString(day: String, night: String): String{
        return if(isNight){
            night
        }else{
            day
        }
    }

    private fun formatRouteTime(time: Int): String {
        val currentTime = System.currentTimeMillis() + time * 1000
        val delegate = "hh:mm aaa"
        val sdf = SimpleDateFormat(delegate, Locale.ENGLISH)
        return sdf.format(currentTime).lowercase(Locale.ENGLISH)
    }

    fun onThemeChange(isNight: Boolean){
        this.isNight = isNight
        initTheme(isSelected)
    }
}




