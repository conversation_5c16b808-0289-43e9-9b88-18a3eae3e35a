package com.link.rideramap.core.weather.data.repository

import com.amap.api.services.weather.LocalWeatherForecastResult
import com.amap.api.services.weather.LocalWeatherLiveResult
import com.amap.api.services.weather.WeatherSearch
import com.amap.api.services.weather.WeatherSearchQuery
import com.link.rideramap.api.RiderMap
import com.link.rideramap.core.weather.domain.entity.AMapWeatherData
import com.link.rideramap.core.weather.domain.repository.AMapWeatherRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * <AUTHOR>
 * @date 2022/8/25
 */

internal class AMapWeatherRepositoryImpl : AMapWeatherRepository {


    override suspend fun getWeather(): Result<AMapWeatherData> = suspendCancellableCoroutine {

        MainScope().launch(Dispatchers.IO) {
            try {
                val location = RiderMap.instance.getLbsLocation()
                val query =
                    WeatherSearchQuery(location.adCode, WeatherSearchQuery.WEATHER_TYPE_LIVE)
                val weatherSearch = WeatherSearch(RiderMap.instance.getApplication())
                weatherSearch.setOnWeatherSearchListener(object :
                    WeatherSearch.OnWeatherSearchListener {
                    override fun onWeatherLiveSearched(
                        weatherLiveResult: LocalWeatherLiveResult?, code: Int
                    ) {
                        if (code == 1000) {
                            val liveResult = weatherLiveResult?.liveResult
                            val weatherData = AMapWeatherData(
                                adCode = liveResult?.adCode ?: "",
                                city = liveResult?.city ?: "",
                                humidity = liveResult?.humidity ?: "",
                                province = liveResult?.province ?: "",
                                reportTime = liveResult?.reportTime ?: "",
                                temperature = liveResult?.temperature ?: "",
                                weather = liveResult?.weather ?: "",
                                windDirection = liveResult?.windDirection ?: "",
                                windPower = liveResult?.windPower ?: ""
                            )
                            it.resume(Result.success(weatherData))
                        }
                    }

                    override fun onWeatherForecastSearched(
                        weatherForecastResult: LocalWeatherForecastResult?, code: Int
                    ) {

                    }
                })
                weatherSearch.query = query
                weatherSearch.searchWeatherAsyn()
            } catch (e: Exception) {
                it.resume(Result.failure(e))
            }
        }
    }

    companion object {
        private const val TAG = "WeatherRepositoryImpl"

    }
}